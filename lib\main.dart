﻿import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:choice_once_upon_a_time/app/app_widget.dart';
import 'firebase_options.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Load environment variables
  try {
    await dotenv.load(fileName: '.env');
  } catch (e) {
    // .env file might not exist in development, that's okay
    debugPrint('Note: .env file not found, using default configuration');
  }

  // Initialize Firebase
  try {
    // Initialize Firebase with platform-specific options
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    debugPrint('Firebase initialized successfully');
  } catch (e) {
    // Firebase initialization failed - app will still work with mock data
    debugPrint('Firebase initialization failed: $e');
    debugPrint('App will continue with mock data');
  }

  runApp(const ProviderScope(child: AppWidget()));
}
