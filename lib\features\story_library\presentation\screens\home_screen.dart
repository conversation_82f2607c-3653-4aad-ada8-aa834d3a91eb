import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';
import 'package:choice_once_upon_a_time/features/story_library/presentation/providers/story_library_provider.dart';
import 'package:choice_once_upon_a_time/features/story_library/presentation/widgets/story_cover_card_widget.dart';
import 'package:choice_once_upon_a_time/app/providers/settings_provider.dart';
import 'package:choice_once_upon_a_time/core/mixins/screen_narrator_mixin.dart';
import 'package:choice_once_upon_a_time/shared_widgets/loading_indicator_widget.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> with ScreenNarratorMixin {
  @override
  void initState() {
    super.initState();
    // Load stories when the screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(storyLibraryProvider.notifier).loadStories();
      // Play screen introduction after stories start loading
      playScreenIntroduction(ref, 'screen_home_library_intro');
    });
  }

  @override
  void dispose() {
    disposeScreenNarrator(ref);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final storyLibraryState = ref.watch(storyLibraryProvider);
    final languageCode = ref.watch(narrationLanguageProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Story Library'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => context.go('/parent_gate_entry'),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () => ref.read(storyLibraryProvider.notifier).refresh(),
        child: _buildBody(context, storyLibraryState, languageCode),
      ),
    );
  }

  Widget _buildBody(BuildContext context, StoryLibraryState state, String languageCode) {
    if (state.isLoading && state.stories.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            LoadingIndicatorWidget(),
            SizedBox(height: 16),
            Text('Loading stories...'),
          ],
        ),
      );
    }

    if (state.error != null && state.stories.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Oops! Something went wrong',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              state.error!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => ref.read(storyLibraryProvider.notifier).refresh(),
              child: const Text('Try Again'),
            ),
          ],
        ),
      );
    }

    if (state.stories.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.book_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'No stories available',
              style: TextStyle(fontSize: 18),
            ),
          ],
        ),
      );
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = MediaQuery.of(context).size.width;
        
        // Responsive grid layout settings
        const horizontalPadding = 16.0;
        const verticalPadding = 12.0;
        const mainAxisSpacing = 16.0;
        const crossAxisSpacing = 16.0;

        // Determine if we should use single column based on portrait mode and screen size
        final isPortrait = MediaQuery.of(context).orientation == Orientation.portrait;
        final isSmallScreen = screenWidth < 600;
        final isOneColumn = isPortrait && isSmallScreen;
        
        // Calculate grid properties based on screen width
        double maxCrossAxisExtent;
        double childAspectRatio;
        
        if (isOneColumn) {
          // Single column mode - card takes full width minus padding
          maxCrossAxisExtent = screenWidth - (horizontalPadding * 2);
          childAspectRatio = 0.45; // Taller cards for single column
        } else if (screenWidth < 900) {
          // Tablet/small desktop - 2 columns
          maxCrossAxisExtent = (screenWidth - (horizontalPadding * 2) - crossAxisSpacing) / 2;
          childAspectRatio = 0.6; // Taller cards for 2 columns
        } else if (screenWidth < 1200) {
          // Desktop - 3 columns
          maxCrossAxisExtent = (screenWidth - (horizontalPadding * 2) - (crossAxisSpacing * 2)) / 3;
          childAspectRatio = 0.7; // More balanced for 3 columns
        } else {
          // Large desktop - 4 columns
          maxCrossAxisExtent = (screenWidth - (horizontalPadding * 2) - (crossAxisSpacing * 3)) / 4;
          childAspectRatio = 0.75; // Slightly wider cards for 4 columns
        }

        return CustomScrollView(
          slivers: [
            SliverPadding(
              padding: EdgeInsets.symmetric(
                horizontal: horizontalPadding,
                vertical: verticalPadding,
              ),
              sliver: SliverGrid(
                gridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
                  maxCrossAxisExtent: maxCrossAxisExtent,
                  childAspectRatio: childAspectRatio,
                  crossAxisSpacing: crossAxisSpacing,
                  mainAxisSpacing: mainAxisSpacing,
                ),
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final story = state.stories[index];
                    return StoryCoverCardWidget(
                      story: story,
                      isOneColumn: isOneColumn,
                      languageCode: languageCode,
                      onTap: () => _onStoryTap(context, story),
                    );
                  },
                  childCount: state.stories.length,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _onStoryTap(BuildContext context, StoryMetadataModel story) {
    // Navigate to story intro screen with dataSource parameter
    context.go('/story/${story.id}?dataSource=${story.dataSource}');
  }
}
