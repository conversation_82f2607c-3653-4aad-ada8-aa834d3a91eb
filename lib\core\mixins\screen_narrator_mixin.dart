import 'dart:developer' as developer;
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/app/providers/service_providers.dart';

/// Mixin to provide screen narrator introduction functionality
/// This mixin can be used by StatefulWidget screens to easily integrate
/// narrator voice introductions with proper lifecycle management
mixin ScreenNarratorMixin<T extends StatefulWidget> on State<T> {
  bool _hasPlayedIntroduction = false;
  bool _isDisposed = false;

  /// Play the narrator introduction for this screen
  /// [screenKey] - The key to look up in screen_narrations_en.json
  /// [delayMs] - Optional delay before playing the introduction (default: 500ms)
  /// [onlyOnce] - If true, will only play once per screen instance (default: true)
  Future<void> playScreenIntroduction(
    WidgetRef ref,
    String screenKey, {
    int delayMs = 500,
    bool onlyOnce = true,
  }) async {
    // Check if we should skip playing
    if (onlyOnce && _hasPlayedIntroduction) {
      developer.log('[ScreenIntroNarrator] Skipping introduction for $screenKey - already played', name: 'ScreenNarratorMixin');
      return;
    }

    if (_isDisposed) {
      developer.log('[ScreenIntroNarrator] Skipping introduction for $screenKey - widget disposed', name: 'ScreenNarratorMixin');
      return;
    }

    try {
      // Add delay to allow screen to settle
      if (delayMs > 0) {
        await Future.delayed(Duration(milliseconds: delayMs));
      }

      // Check again if disposed after delay
      if (_isDisposed || !mounted) {
        developer.log('[ScreenIntroNarrator] Skipping introduction for $screenKey - widget disposed during delay', name: 'ScreenNarratorMixin');
        return;
      }

      // Get services
      final screenNarrationService = ref.read(screenNarrationServiceProvider);
      final ttsService = ref.read(ttsServiceProvider);

      // Get narration for this screen
      final narration = screenNarrationService.getNarrationForScreen(screenKey);
      if (narration == null) {
        developer.log('[ScreenIntroNarrator] No narration found for screen: $screenKey', name: 'ScreenNarratorMixin');
        return;
      }

      developer.log('[ScreenIntroNarrator] Playing introduction for $screenKey: "${narration.text}"', name: 'ScreenNarratorMixin');

      // Play the introduction
      final success = await ttsService.speakScreenIntroduction(
        text: narration.text,
        emotionCue: narration.emotionCue,
      );

      if (success) {
        _hasPlayedIntroduction = true;
        developer.log('[ScreenIntroNarrator] Successfully started introduction for $screenKey', name: 'ScreenNarratorMixin');
      } else {
        developer.log('[ScreenIntroNarrator] Failed to start introduction for $screenKey', name: 'ScreenNarratorMixin');
      }
    } catch (e) {
      developer.log('[ScreenIntroNarrator] Error playing introduction for $screenKey: $e', name: 'ScreenNarratorMixin');
    }
  }

  /// Stop any currently playing screen introduction
  /// This should be called when the user navigates away or the screen is disposed
  Future<void> stopScreenIntroduction(WidgetRef ref) async {
    if (_isDisposed) return;

    try {
      final ttsService = ref.read(ttsServiceProvider);
      if (ttsService.isSpeaking) {
        await ttsService.stop();
        developer.log('[ScreenIntroNarrator] Stopped screen introduction due to navigation/disposal', name: 'ScreenNarratorMixin');
      }
    } catch (e) {
      developer.log('[ScreenIntroNarrator] Error stopping screen introduction: $e', name: 'ScreenNarratorMixin');
    }
  }

  /// Call this in the widget's dispose method
  void disposeScreenNarrator(WidgetRef ref) {
    _isDisposed = true;
    // Stop any playing introduction when disposing
    stopScreenIntroduction(ref);
  }

  /// Reset the introduction state (useful for testing or special cases)
  void resetIntroductionState() {
    _hasPlayedIntroduction = false;
  }

  /// Check if introduction has been played
  bool get hasPlayedIntroduction => _hasPlayedIntroduction;

  /// Check if the widget is disposed
  bool get isDisposed => _isDisposed;
}
