// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Choice: Once Upon A Time';

  @override
  String get ftueScreenWelcome => 'Welcome to Choice! Let me show you how to start your interactive story adventure.';

  @override
  String get ftueFeatureInteractiveStories => 'Our interactive stories let you choose your own path in every tale.';

  @override
  String get ftueFeatureLifeValues => 'These stories teach and inspire, reinforcing positive life values.';

  @override
  String get ftueFeatureNarration => 'Listen to your stories come alive with our empathetic narrator.';

  @override
  String get ftueCompletePrompt => 'Are you ready? Tap \'Start Reading\' to begin!';

  @override
  String get ftueSkipPrompt => 'Or, if you\'re ready, you can skip the tutorial.';
}
