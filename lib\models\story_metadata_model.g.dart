// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'story_metadata_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StoryMetadataModel _$StoryMetadataModelFromJson(Map<String, dynamic> json) =>
    StoryMetadataModel(
      id: json['id'] as String,
      title: Map<String, String>.from(json['title'] as Map),
      coverImageUrl: json['coverImageUrl'] as String,
      loglineShort: Map<String, String>.from(json['loglineShort'] as Map),
      targetMoralValue: json['targetMoralValue'] as String,
      version: json['version'] as String? ?? '1.0.0',
      isNew: json['isNew'] as bool? ?? false,
      hasUpdate: json['hasUpdate'] as bool? ?? false,
      isLocked: json['isLocked'] as bool? ?? true,
      supportedLanguages: (json['supportedLanguages'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const ['en-US'],
      defaultLanguage: json['defaultLanguage'] as String? ?? 'en-US',
      isFree: json['isFree'] as bool? ?? false,
      estimatedDurationMinutes:
          (json['estimatedDurationMinutes'] as num?)?.toInt() ?? 10,
      published: json['published'] as bool? ?? true,
      targetAgeSubSegment: json['targetAgeSubSegment'] as String,
      initialSceneId: json['initialSceneId'] as String,
      dataSource: json['dataSource'] as String? ?? 'asset',
      estimatedSizeMb: (json['estimatedSizeMb'] as num?)?.toInt() ?? 20,
    );

Map<String, dynamic> _$StoryMetadataModelToJson(StoryMetadataModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'coverImageUrl': instance.coverImageUrl,
      'loglineShort': instance.loglineShort,
      'targetMoralValue': instance.targetMoralValue,
      'version': instance.version,
      'isNew': instance.isNew,
      'hasUpdate': instance.hasUpdate,
      'isLocked': instance.isLocked,
      'supportedLanguages': instance.supportedLanguages,
      'defaultLanguage': instance.defaultLanguage,
      'isFree': instance.isFree,
      'estimatedDurationMinutes': instance.estimatedDurationMinutes,
      'published': instance.published,
      'targetAgeSubSegment': instance.targetAgeSubSegment,
      'initialSceneId': instance.initialSceneId,
      'dataSource': instance.dataSource,
      'estimatedSizeMb': instance.estimatedSizeMb,
    };
