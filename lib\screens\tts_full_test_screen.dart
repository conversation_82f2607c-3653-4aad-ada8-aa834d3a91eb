import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/core/audio/flutter_tts_service.dart'; // Your actual path to the service
import 'package:choice_once_upon_a_time/models/text_segment_model.dart'; // Your actual path to TextSegmentModel
import 'package:choice_once_upon_a_time/core/audio/tts_service_interface.dart'; // Your actual path to TTSState, TTSVoice, TTSSpeechParameters
import 'package:choice_once_upon_a_time/core/audio/emotion_cue_mapper_service.dart'; // Your actual path to EmotionCueMapperService
import 'dart:async'; // For StreamSubscription

class TtsFullTestScreen extends StatefulWidget {
  const TtsFullTestScreen({super.key});

  @override
  State<TtsFullTestScreen> createState() => _TtsFullTestScreenState();
}

class _TtsFullTestScreenState extends State<TtsFullTestScreen> {
  final FlutterTTSService _ttsService = FlutterTTSService();
  final TextEditingController _textController = TextEditingController(
      text:
          "Hello, Dream Land. This is a comprehensive test of our empathetic narrator service. We can try different emotions too!");
  TTSState _currentState = TTSState.stopped;
  String _currentWord = '';
  double _currentProgress = 0.0;
  List<TTSVoice> _availableVoices = [];
  String? _selectedVoiceId;
  String? _selectedEmotionCue = 'neutral';

  late StreamSubscription<TTSState> _stateSubscription;
  late StreamSubscription<double> _progressSubscription;
  late StreamSubscription<String> _wordBoundarySubscription;

  List<String> _emotionCues = [];

  @override
  void initState() {
    super.initState();
    _initializeTtsService();
    _subscribeToTtsStreams();
    _emotionCues = EmotionCueMapperService.getAllEmotionCues();
  }

  Future<void> _initializeTtsService() async {
    final success = await _ttsService.initialize();
    if (success) {
      print("TTS Service Initialized Successfully!");
      final voices = await _ttsService.getAvailableVoices();
      setState(() {
        _availableVoices = voices;
        if (voices.isNotEmpty) {
          // Try to find a default English voice, otherwise pick first
          _selectedVoiceId = voices.firstWhere(
            (v) => v.language.toLowerCase().contains('en') && v.isDefault,
            orElse: () => voices.first,
          ).id;
          _ttsService.setVoice(_selectedVoiceId!);
        }
      });
      print("Available Voices: $_availableVoices");
    } else {
      print("TTS Service Initialization Failed!");
    }
  }

  void _subscribeToTtsStreams() {
    _stateSubscription = _ttsService.stateStream.listen((state) {
      setState(() {
        _currentState = state;
      });
      print("TTS State Changed: $state");
    });

    _progressSubscription = _ttsService.progressStream.listen((progress) {
      setState(() {
        _currentProgress = progress;
      });
    });

    _wordBoundarySubscription = _ttsService.wordBoundaryStream.listen((word) {
      setState(() {
        _currentWord = word;
      });
    });
  }

  @override
  void dispose() {
    _stateSubscription.cancel();
    _progressSubscription.cancel();
    _wordBoundarySubscription.cancel();
    _ttsService.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Flutter TTS Service Full Test'),
        backgroundColor: Colors.deepPurple,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildStatusCard(),
            const SizedBox(height: 20),
            TextField(
              controller: _textController,
              decoration: const InputDecoration(
                labelText: 'Text to Speak',
                border: OutlineInputBorder(),
                suffixIcon: Icon(Icons.text_fields),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 10),
            _buildEmotionDropdown(),
            const SizedBox(height: 10),
            ElevatedButton.icon(
              icon: const Icon(Icons.volume_up),
              label: const Text('Speak Text with Selected Emotion'),
              onPressed: _ttsService.isAvailable && !_ttsService.isSpeaking
                  ? () => _ttsService.speakText(_textController.text,
                      emotionCue: _selectedEmotionCue)
                  : null,
              style: ElevatedButton.styleFrom(backgroundColor: Colors.green[700]),
            ),
            const SizedBox(height: 20),
            _buildEmotionSpecificButtons(),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.pause),
                    label: const Text('Pause'),
                    onPressed: _ttsService.isAvailable && _ttsService.isSpeaking
                        ? _ttsService.pause
                        : null,
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.amber[700]),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.play_arrow),
                    label: const Text('Resume'),
                    onPressed: _ttsService.isAvailable && _ttsService.isPaused
                        ? _ttsService.resume
                        : null,
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.lightBlue[700]),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.stop),
                    label: const Text('Stop'),
                    onPressed: _ttsService.isAvailable &&
                            (_ttsService.isSpeaking || _ttsService.isPaused)
                        ? _ttsService.stop
                        : null,
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.red[700]),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildVoiceSelection(),
            const SizedBox(height: 20),
            _buildProgressIndicator(),
            const SizedBox(height: 10),
            Text(
              'Current Word: $_currentWord',
              style: const TextStyle(fontSize: 16, fontStyle: FontStyle.italic),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      elevation: 4,
      color: Colors.deepPurple[50],
      margin: EdgeInsets.zero,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'TTS Service Status:',
              style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.deepPurple[900]),
            ),
            const SizedBox(height: 8),
            Text(
              'State: ${_currentState.toString().split('.').last}',
              style: TextStyle(fontSize: 16, color: _getStatusColor(_currentState)),
            ),
            Text(
              'Is Initialized: ${_ttsService.isAvailable}',
              style: const TextStyle(fontSize: 16),
            ),
            Text(
              'Is Speaking: ${_ttsService.isSpeaking}',
              style: const TextStyle(fontSize: 16),
            ),
            Text(
              'Is Paused: ${_ttsService.isPaused}',
              style: const TextStyle(fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(TTSState state) {
    switch (state) {
      case TTSState.playing:
        return Colors.green;
      case TTSState.paused:
        return Colors.orange;
      case TTSState.stopped:
        return Colors.blue;
      case TTSState.loading:
        return Colors.grey;
      case TTSState.error:
        return Colors.red;
      default:
        return Colors.black;
    }
  }

  Widget _buildEmotionDropdown() {
    return DropdownButtonFormField<String>(
      decoration: const InputDecoration(
        labelText: 'Select Emotion Cue',
        border: OutlineInputBorder(),
      ),
      value: _selectedEmotionCue,
      onChanged: (String? newValue) {
        setState(() {
          _selectedEmotionCue = newValue;
        });
      },
      items: _emotionCues.map((String emotion) {
        return DropdownMenuItem<String>(
          value: emotion,
          child: Text(
              '$emotion (${EmotionCueMapperService.getEmotionDescription(emotion)})'),
        );
      }).toList(),
    );
  }

  Widget _buildEmotionSpecificButtons() {
    return Wrap(
      spacing: 8.0, // horizontal gap between adjacent chips
      runSpacing: 4.0, // vertical gap between lines
      children: [
        _buildEmotionButton(
            'happy', "I feel so wonderful and bright!"), // Corrected string literal
        _buildEmotionButton('sad', 'A gentle sadness fills the air.'),
        _buildEmotionButton('calm', 'All is peaceful and calm.'),
        _buildEmotionButton(
            'excited', 'Oh, I\'m so excited for this adventure!'), // Corrected string literal
        _buildEmotionButton('mysterious', 'A mysterious whisper from the shadows.'),
        _buildEmotionButton('wise', 'Let me share some wise words with you.'),
      ],
    );
  }

  Widget _buildEmotionButton(String emotion, String text) {
    return ElevatedButton(
      onPressed: _ttsService.isAvailable && !_ttsService.isSpeaking
          ? () {
              final segment = TextSegmentModel(
                id: 'test_$emotion',
                text: {'en': text}, // <--- CHANGED FROM 'localizedText' to 'text'
                emotionCue: emotion,
              );
              _ttsService.speakSegment(segment, 'en');
            }
          : null,
      child: Text('${emotion[0].toUpperCase()}${emotion.substring(1)}'),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.indigo[100],
        foregroundColor: Colors.black87,
      ),
    );
  }

  Widget _buildVoiceSelection() {
    if (_availableVoices.isEmpty && _ttsService.isAvailable) {
      return const Text(
          'No voices available on device. Check device settings.',
          style: TextStyle(color: Colors.red));
    }
    if (!_ttsService.isAvailable) {
      return const SizedBox.shrink(); // Hide if service not initialized
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Select Voice:',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
        DropdownButton<String>(
          isExpanded: true,
          value: _selectedVoiceId,
          hint: const Text('Select a voice'),
          onChanged: (String? newValue) {
            if (newValue != null) {
              setState(() {
                _selectedVoiceId = newValue;
              });
              _ttsService.setVoice(newValue);
            }
          },
          items: _availableVoices.map<DropdownMenuItem<String>>((TTSVoice voice) {
            return DropdownMenuItem<String>(
              value: voice.id,
              child: Text('${voice.name} (${voice.language})'),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildProgressIndicator() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Speech Progress:',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
        LinearProgressIndicator(
          value: _currentProgress,
          backgroundColor: Colors.grey[300],
          valueColor: const AlwaysStoppedAnimation<Color>(Colors.deepPurpleAccent),
        ),
        Text(
          '${(_currentProgress * 100).toStringAsFixed(1)}%',
          textAlign: TextAlign.right,
          style: const TextStyle(fontSize: 14),
        ),
      ],
    );
  }
}