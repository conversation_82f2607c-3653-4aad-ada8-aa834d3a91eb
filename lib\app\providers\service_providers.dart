import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/core/storage/offline_storage_service_stub.dart';
import 'package:choice_once_upon_a_time/core/audio/tts_service_interface.dart';
import 'package:choice_once_upon_a_time/core/audio/flutter_tts_service.dart';
import 'package:choice_once_upon_a_time/core/audio/sound_effect_player_service.dart';
import 'package:choice_once_upon_a_time/features/story_library/data/story_repository.dart';

/// Provider for the offline storage service
final offlineStorageServiceProvider = Provider<OfflineStorageService>((ref) {
  return OfflineStorageService();
});

/// Provider for the TTS service interface
final ttsServiceProvider = Provider<TTSServiceInterface>((ref) {
  return FlutterTTSService();
});

/// Provider for the sound effect player service
final soundEffectPlayerServiceProvider = Provider<SoundEffectPlayerService>((ref) {
  return SoundEffectPlayerService();
});

/// Provider for the story repository
final storyRepositoryProvider = Provider<StoryRepository>((ref) {
  return StoryRepository();
});

/// Provider to initialize all services
final servicesInitializationProvider = FutureProvider<void>((ref) async {
  // Initialize offline storage
  final offlineStorage = ref.read(offlineStorageServiceProvider);
  await offlineStorage.initDatabase();

  // Initialize TTS service
  final ttsService = ref.read(ttsServiceProvider);
    final ttsInitialized = await ttsService.initialize();
  if (!ttsInitialized) {
    print("Services: TTS Service failed to initialize!");
    // You might want to throw an exception or handle this error more gracefully
    // depending on app's robustness requirements.
  } else {
    print("Services: TTS Service initialized.");
  }

  // Preload UI sounds
  //final soundService = ref.read(soundEffectPlayerServiceProvider);
  //await soundService.preloadUISounds();
});
