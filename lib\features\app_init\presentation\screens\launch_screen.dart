import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:animated_text_kit/animated_text_kit.dart'; // Added for text animation
import 'package:choice_once_upon_a_time/shared_widgets/loading_indicator_widget.dart';
import 'package:choice_once_upon_a_time/app/providers/settings_provider.dart'; // Assuming ftueCompletedProvider is here
import 'package:choice_once_upon_a_time/app/providers/service_providers.dart'; // Assuming servicesInitializationProvider is here
import 'package:choice_once_upon_a_time/core/mixins/screen_narrator_mixin.dart';

class LaunchScreen extends ConsumerStatefulWidget {
  const LaunchScreen({super.key});

  @override
  ConsumerState<LaunchScreen> createState() => _LaunchScreenState();
}

class _LaunchScreenState extends ConsumerState<LaunchScreen> with ScreenNarratorMixin {
  bool _isTextAnimationVisible = false; // Control visibility of animated text

  @override
  void initState() {
    super.initState();
    _startLaunchSequence();
  }

  Future<void> _startLaunchSequence() async {
    // 1. Wait for core services to initialize first
    await ref.read(servicesInitializationProvider.future);

    // 2. Check if this is a returning user to choose appropriate narration
    final ftueCompleted = ref.read(ftueCompletedProvider);
    final screenKey = ftueCompleted ? 'screen_launch_welcome_returning' : 'screen_launch_welcome';

    // 3. Play the screen introduction narration
    await playScreenIntroduction(ref, screenKey, delayMs: 200);

    // 4. Wait a bit for the TTS to start before showing text animation
    await Future.delayed(const Duration(milliseconds: 500));

    if (!mounted) return;

    setState(() {
      _isTextAnimationVisible = true; // Show animated text
    });

    // 5. Wait for the text animation to complete (managed by AnimatedTextKit's onFinished)
    // The navigation logic will be called from _onTextAnimationComplete()
  }

  // Callback from AnimatedTextKit when animation finishes
  Future<void> _onTextAnimationComplete() async {
    // Ensure TTS has finished or is near completion.
    // Given the short duration of the splash text and initial TTS, a small delay
    // here allows the audio to fully play out.
    await Future.delayed(const Duration(seconds: 1));

    if (!mounted) return;

    // Check if FTUE is completed
    final ftueCompleted = ref.read(ftueCompletedProvider);

    if (ftueCompleted) {
      context.go('/home');
    } else {
      context.go('/ftue');
    }
  }

  @override
  void dispose() {
    disposeScreenNarrator(ref);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App Logo/Icon
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: theme.colorScheme.primary,
                borderRadius: BorderRadius.circular(24),
              ),
              child: const Icon(
                Icons.auto_stories,
                size: 64,
                color: Colors.white,
              ),
            ),

            const SizedBox(height: 32),

            // Animated App Name
            if (_isTextAnimationVisible)
              SizedBox(
                width: MediaQuery.of(context).size.width * 0.8,
                child: AnimatedTextKit(
                  animatedTexts: [
                    TypewriterAnimatedText(
                      'Choice: Once Upon A Time',
                      textStyle: theme.textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.primary,
                      ),
                      speed: const Duration(milliseconds: 100),
                    ),
                    TypewriterAnimatedText(
                      'Interactive bedtime stories for children',
                      textStyle: theme.textTheme.bodyLarge?.copyWith(
                        color: Colors.grey[600],
                      ),
                      speed: const Duration(milliseconds: 80),
                    ),
                  ],
                  totalRepeatCount: 1, // Play once
                  pause: const Duration(milliseconds: 1000), // Pause between lines
                  onFinished: _onTextAnimationComplete, // Call navigation after animation
                  displayFullTextOnTap: false,
                  stopPauseOnTap: false,
                ),
              )
            else
              // Placeholder for when animation is not yet visible
              Column(
                children: [
                  Text(
                    'Choice: Once Upon A Time',
                    style: theme.textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Interactive bedtime stories for children',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),

            const SizedBox(height: 48),

            const LoadingIndicatorWidget(),

            const SizedBox(height: 24),

            Text(
              'Loading...',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }
}