// lib/core/audio/voice_guidance_manager.dart

import 'package:flutter/widgets.dart'; // For BuildContext
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/l10n/app_localizations.dart'; // For accessing localized strings
// import 'package:choice_once_upon_a_time/services/flutter_tts_service.dart'; // No longer directly imported here if using ttsServiceProvider
import 'package:choice_once_upon_a_time/app/providers/service_providers.dart'; // Assuming ttsServiceProvider is defined here
import 'package:choice_once_upon_a_time/core/audio/tts_service_interface.dart'; // Required for TTSServiceInterface type

/// Provider for VoiceGuidanceManager
final voiceGuidanceManagerProvider = Provider<VoiceGuidanceManager>((ref) {
  final ttsService = ref.read(ttsServiceProvider); // This will provide TTSServiceInterface
  return VoiceGuidanceManager(ttsService);
});

/// Manages the playback of UI-specific voice guidance.
/// This service provides a centralized way to trigger and control voice prompts
/// for various screens, using localized text.
class VoiceGuidanceManager {
  // CHANGE THIS LINE: Use the interface type
  final TTSServiceInterface _ttsService; // <--- Change Type to TTSServiceInterface
  
  // Constructor also uses the interface type
  VoiceGuidanceManager(this._ttsService); // <--- Constructor parameter now matches

  /// Plays a voice guide for a specific screen/context.
  ///
  /// [context]: BuildContext to resolve AppLocalizations.
  /// [getString]: A function that takes AppLocalizations and returns the string to speak.
  /// [interpolationArgs]: Optional arguments for string interpolation.
  Future<void> playGuide(BuildContext context, String Function(AppLocalizations) getString, {Map<String, dynamic>? interpolationArgs}) async {
    await _ttsService.stop();

    final appLocalizations = AppLocalizations.of(context)!;
    String textToSpeak = getString(appLocalizations);

    // Note: If you need interpolationArgs, you'll need to update how getString is defined
    // to accept parameters, e.g., appLocalizations.someKey(arg1: value, arg2: value)
    // For now, this assumes simple string access.

    if (textToSpeak.isNotEmpty) {
      await _ttsService.speakText(textToSpeak, emotionCue: 'encouraging');
      print('VoiceGuide: Playing "$textToSpeak"');
    }
  }

  /// Stops any currently playing voice guidance.
  Future<void> stopGuide() async {
    await _ttsService.stop();
    print('VoiceGuide: Stopped playback.');
  }

  /// Checks if voice guidance is currently speaking.
  bool get isSpeaking => _ttsService.isSpeaking;
}