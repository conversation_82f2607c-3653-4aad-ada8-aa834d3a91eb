import 'dart:async';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:choice_once_upon_a_time/models/text_segment_model.dart';
import 'tts_service_interface.dart';
import 'emotion_cue_mapper_service.dart';

/// Flutter TTS implementation of the TTS service interface
/// This provides on-device text-to-speech using the flutter_tts package
class FlutterTTSService implements TTSServiceInterface {
  static final FlutterTTSService _instance = FlutterTTSService._internal();
  factory FlutterTTSService() => _instance;
  FlutterTTSService._internal();

  FlutterTts? _flutterTts;
  bool _isInitialized = false;
  TTSState _currentState = TTSState.stopped;
  String? _currentLanguage;
  TTSSpeechParameters _currentParameters = const TTSSpeechParameters();

  // Stream controllers for state and progress updates
  final StreamController<TTSState> _stateController = StreamController<TTSState>.broadcast();
  final StreamController<double> _progressController = StreamController<double>.broadcast();
  final StreamController<String> _wordBoundaryController = StreamController<String>.broadcast();

  @override
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      _flutterTts = FlutterTts();
      
      // Set up event handlers
      _flutterTts!.setStartHandler(() {
        _updateState(TTSState.playing);
      });

      _flutterTts!.setCompletionHandler(() {
        _updateState(TTSState.stopped);
      });

      _flutterTts!.setErrorHandler((msg) {
        print('TTS Error: $msg');
        _updateState(TTSState.error);
      });

      _flutterTts!.setPauseHandler(() {
        _updateState(TTSState.paused);
      });

      _flutterTts!.setContinueHandler(() {
        _updateState(TTSState.playing);
      });

      // Set up progress handler if available
      _flutterTts!.setProgressHandler((String text, int start, int end, String word) {
        _wordBoundaryController.add(word);
        // Calculate progress based on character position
        if (text.isNotEmpty) {
          final progress = end / text.length;
          _progressController.add(progress.clamp(0.0, 1.0));
        }
      });

      // Set default parameters
      await setLanguage('en-US');
      await setSpeechParameters(const TTSSpeechParameters());

      _isInitialized = true;
      _updateState(TTSState.stopped);
      print('TTS: Flutter TTS Service initialized successfully');
      return true;
    } catch (e) {
      print('TTS: Failed to initialize Flutter TTS: $e');
      _isInitialized = false;
      _updateState(TTSState.error);
      return false;
    }
  }

  @override
  Future<bool> setLanguage(String languageCode) async {
    if (_flutterTts == null) return false;

    try {
      await _flutterTts!.setLanguage(languageCode);
      _currentLanguage = languageCode;
      print('TTS: Language set to $languageCode');
      return true;
    } catch (e) {
      print('TTS: Error setting language to $languageCode: $e');
      return false;
    }
  }

  @override
  Future<List<TTSVoice>> getAvailableVoices() async {
    if (_flutterTts == null) return [];

    try {
      final voices = await _flutterTts!.getVoices;
      return voices.map<TTSVoice>((voice) {
        return TTSVoice(
          id: voice['name'] ?? '',
          name: voice['name'] ?? '',
          language: voice['locale'] ?? '',
          gender: voice['gender'],
        );
      }).toList();
    } catch (e) {
      print('TTS: Error getting voices: $e');
      return [];
    }
  }

  @override
  Future<bool> setVoice(String voiceId) async {
    if (_flutterTts == null) return false;

    try {
      await _flutterTts!.setVoice({'name': voiceId, 'locale': _currentLanguage ?? 'en-US'});
      print('TTS: Voice set to $voiceId');
      return true;
    } catch (e) {
      print('TTS: Error setting voice to $voiceId: $e');
      return false;
    }
  }

  @override
  Future<void> setSpeechParameters(TTSSpeechParameters parameters) async {
    if (_flutterTts == null) return;

    try {
      await _flutterTts!.setSpeechRate(parameters.rate);
      await _flutterTts!.setVolume(parameters.volume);
      await _flutterTts!.setPitch(parameters.pitch);
      _currentParameters = parameters;
      print('TTS: Speech parameters updated - pitch: ${parameters.pitch}, rate: ${parameters.rate}, volume: ${parameters.volume}');
    } catch (e) {
      print('TTS: Error setting speech parameters: $e');
    }
  }

  @override
  Future<bool> speakSegment(TextSegmentModel segment, String languageCode) async {
    if (_flutterTts == null) {
      await initialize();
      if (_flutterTts == null) return false;
    }

    try {
      _updateState(TTSState.loading);

      // Get the text for the specified language
      final text = segment.getLocalizedText(languageCode);
      
      // Apply emotion-based modulation
      final emotionParams = EmotionCueMapperService.getParametersForEmotion(segment.emotionCue);
      await setSpeechParameters(emotionParams);
      
      // Speak the text
      await _flutterTts!.speak(text);
      print('TTS: Speaking segment "${segment.id}" with emotion: ${segment.emotionCue}');
      return true;
    } catch (e) {
      print('TTS: Error speaking segment: $e');
      _updateState(TTSState.error);
      return false;
    }
  }

  @override
  Future<bool> speakText(String text, {String? emotionCue}) async {
    if (_flutterTts == null) {
      await initialize();
      if (_flutterTts == null) return false;
    }

    try {
      _updateState(TTSState.loading);

      // Apply emotion-based modulation if provided
      if (emotionCue != null) {
        final emotionParams = EmotionCueMapperService.getParametersForEmotion(emotionCue);
        await setSpeechParameters(emotionParams);
      }
      
      // Speak the text
      await _flutterTts!.speak(text);
      print('TTS: Speaking text with emotion: $emotionCue');
      return true;
    } catch (e) {
      print('TTS: Error speaking text: $e');
      _updateState(TTSState.error);
      return false;
    }
  }

  @override
  Future<bool> speakScreenIntroduction({required String text, required String emotionCue}) async {
    print("[ScreenIntroNarrator] TTS Service: speakScreenIntroduction called. _flutterTts is ${_flutterTts == null ? 'null' : 'not null'}. Text length: ${text.length}");
    if (_flutterTts == null) {
      print("[ScreenIntroNarrator] TTS Service: _flutterTts is null, attempting re-initialize before speaking screen intro.");
      await initialize();
      if (_flutterTts == null) {
        print("[ScreenIntroNarrator] TTS Service: Re-initialization failed. Cannot speak screen introduction.");
        return false;
      }
    }

    try {
      _updateState(TTSState.loading);
      final emotionParams = EmotionCueMapperService.getParametersForEmotion(emotionCue);
      await setSpeechParameters(emotionParams);

      await _flutterTts!.speak(text);
      print('[ScreenIntroNarrator] TTS Service: Speaking screen introduction. Emotion: $emotionCue. Text: "${text.substring(0, text.length > 50 ? 50 : text.length)}..."');
      return true;
    } catch (e) {
      print('[ScreenIntroNarrator] TTS Service: Error speaking screen introduction: $e');
      _updateState(TTSState.error);
      return false;
    }
  }

  @override
  Future<void> pause() async {
    if (_flutterTts != null && _currentState == TTSState.playing) {
      try {
        await _flutterTts!.pause();
        print('TTS: Speech paused');
      } catch (e) {
        print('TTS: Error pausing speech: $e');
      }
    }
  }

  @override
  Future<void> resume() async {
    if (_flutterTts != null && _currentState == TTSState.paused) {
      try {
        // Note: flutter_tts doesn't have a direct resume method on all platforms
        // This implementation may vary by platform
        print('TTS: Attempting to resume speech');
        _updateState(TTSState.playing);
      } catch (e) {
        print('TTS: Error resuming speech: $e');
      }
    }
  }

  @override
  Future<void> stop() async {
    if (_flutterTts != null) {
      try {
        await _flutterTts!.stop();
        print('TTS: Speech stopped');
      } catch (e) {
        print('TTS: Error stopping speech: $e');
      }
    }
  }

  @override
  TTSState getCurrentState() => _currentState;

  @override
  bool get isSpeaking => _currentState == TTSState.playing;

  @override
  bool get isPaused => _currentState == TTSState.paused;

  @override
  bool get isAvailable => _isInitialized && _flutterTts != null;

  @override
  double? get speechProgress => null; // Progress tracking varies by platform

  @override
  Stream<TTSState> get stateStream => _stateController.stream;

  @override
  Stream<double> get progressStream => _progressController.stream;

  @override
  Stream<String> get wordBoundaryStream => _wordBoundaryController.stream;

  @override
  Future<void> dispose() async {
    await stop();
    await _stateController.close();
    await _progressController.close();
    await _wordBoundaryController.close();
    _flutterTts = null;
    _isInitialized = false;
    _updateState(TTSState.stopped);
    print('TTS: Flutter TTS Service disposed');
  }

  /// Update the current state and notify listeners
  void _updateState(TTSState newState) {
    if (_currentState != newState) {
      _currentState = newState;
      _stateController.add(newState);
    }
  }
}
