import 'package:choice_once_upon_a_time/models/text_segment_model.dart';

/// Enumeration of possible TTS states
enum TTSState {
  stopped,
  playing,
  paused,
  loading,
  error,
}

/// Data class for TTS voice information
class TTSVoice {
  final String id;
  final String name;
  final String language;
  final String? gender;
  final bool isDefault;

  const TTSVoice({
    required this.id,
    required this.name,
    required this.language,
    this.gender,
    this.isDefault = false,
  });

  @override
  String toString() => 'TTSVoice(id: $id, name: $name, language: $language)';
}

/// Data class for TTS speech parameters
class TTSSpeechParameters {
  final double pitch;
  final double rate;
  final double volume;

  const TTSSpeechParameters({
    this.pitch = 1.0,
    this.rate = 0.5,
    this.volume = 1.0,
  });

  TTSSpeechParameters copyWith({
    double? pitch,
    double? rate,
    double? volume,
  }) {
    return TTSSpeechParameters(
      pitch: pitch ?? this.pitch,
      rate: rate ?? this.rate,
      volume: volume ?? this.volume,
    );
  }

  @override
  String toString() => 'TTSSpeechParameters(pitch: $pitch, rate: $rate, volume: $volume)';
}

/// Abstract interface for Text-to-Speech services
/// This allows for easy swapping between different TTS implementations
/// (flutter_tts, server-based TTS, advanced native TTS, etc.)
abstract class TTSServiceInterface {
  /// Initialize the TTS service
  /// Returns true if initialization was successful
  Future<bool> initialize();

  /// Set the language for TTS
  /// Returns true if the language was set successfully
  Future<bool> setLanguage(String languageCode);

  /// Get available voices for the current language
  Future<List<TTSVoice>> getAvailableVoices();

  /// Set the voice to use for TTS
  /// Returns true if the voice was set successfully
  Future<bool> setVoice(String voiceId);

  /// Set speech parameters (pitch, rate, volume)
  Future<void> setSpeechParameters(TTSSpeechParameters parameters);

  /// Speak a text segment with emotion modulation
  /// Returns true if speech started successfully
  Future<bool> speakSegment(TextSegmentModel segment, String languageCode);

  /// Speak plain text with optional emotion cue
  /// Returns true if speech started successfully
  Future<bool> speakText(String text, {String? emotionCue});

  /// Speak screen introduction text with emotion cue
  /// Specifically designed for UI screen introductions
  /// Returns true if speech started successfully
  Future<bool> speakScreenIntroduction({required String text, required String emotionCue});

  /// Pause the current speech
  Future<void> pause();

  /// Resume paused speech
  Future<void> resume();

  /// Stop the current speech
  Future<void> stop();

  /// Get the current TTS state
  TTSState getCurrentState();

  /// Check if TTS is currently speaking
  bool get isSpeaking;

  /// Check if TTS is currently paused
  bool get isPaused;

  /// Check if TTS is available on this platform
  bool get isAvailable;

  /// Get the current speech progress (0.0 to 1.0)
  /// Returns null if progress tracking is not supported
  double? get speechProgress;

  /// Stream of TTS state changes
  Stream<TTSState> get stateStream;

  /// Stream of speech progress updates (0.0 to 1.0)
  /// May be empty if progress tracking is not supported
  Stream<double> get progressStream;

  /// Stream of word boundaries during speech
  /// Useful for text highlighting
  Stream<String> get wordBoundaryStream;

  /// Dispose of resources
  Future<void> dispose();
}

/// Exception thrown when TTS operations fail
class TTSException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;

  const TTSException(this.message, {this.code, this.originalError});

  @override
  String toString() => 'TTSException: $message${code != null ? ' (code: $code)' : ''}';
}
